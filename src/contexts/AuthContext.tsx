import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

// Local type definitions to avoid import issues
interface User {
  id: string;
  email: string;
  password: string;
  role: 'admin' | 'teacher' | 'student';
  firstName: string;
  lastName: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  permissions?: string[];
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  error: string | null;
}

// Simple demo users
const demoUsers: User[] = [
  {
    id: 'admin-1',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    firstName: 'John',
    lastName: 'Administrator',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true,
    permissions: ['manage_users', 'manage_classes', 'view_reports', 'system_settings']
  },
  {
    id: 'teacher-1',
    email: '<EMAIL>',
    password: 'teacher123',
    role: 'teacher',
    firstName: 'Sarah',
    lastName: '<PERSON>',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true
  },
  {
    id: 'student-1',
    email: '<EMAIL>',
    password: 'student123',
    role: 'student',
    firstName: 'Emma',
    lastName: 'Wilson',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    isActive: true
  }
];

// Auth state interface
interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

// Auth actions
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGOUT' };

// Initial state
const initialState: AuthState = {
  user: null,
  isLoading: false,
  error: null
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload, error: null };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'LOGOUT':
      return { ...state, user: null, error: null };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}



// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Simple login function
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Find user in demo users
      const user = demoUsers.find(
        u => u.email.toLowerCase() === email.toLowerCase() && u.password === password
      );

      if (!user) {
        dispatch({ type: 'SET_ERROR', payload: 'Invalid email or password' });
        return false;
      }

      if (!user.isActive) {
        dispatch({ type: 'SET_ERROR', payload: 'Account is deactivated. Please contact administrator.' });
        return false;
      }

      dispatch({ type: 'SET_USER', payload: user });
      return true;
    } catch (error) {
      console.error('Login error:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Login failed. Please try again.' });
      return false;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Simple logout function
  const logout = () => {
    dispatch({ type: 'LOGOUT' });
  };

  // Context value
  const contextValue: AuthContextType = {
    user: state.user,
    login,
    logout,
    isLoading: state.isLoading,
    error: state.error
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
