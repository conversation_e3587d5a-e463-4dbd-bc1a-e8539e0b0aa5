import React, { InputHTMLAttributes, forwardRef, useState } from 'react';
import { LucideIcon, Eye, EyeOff } from 'lucide-react';

// Input props interface
interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: LucideIcon;
  rightIcon?: LucideIcon;
  isLoading?: boolean;
  fullWidth?: boolean;
}

// Input component
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      leftIcon: LeftIcon,
      rightIcon: RightIcon,
      isLoading = false,
      fullWidth = true,
      className = '',
      type = 'text',
      disabled,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const isPassword = type === 'password';
    const inputType = isPassword && showPassword ? 'text' : type;

    // Base input classes
    const baseClasses = [
      'block',
      'w-full',
      'rounded-md',
      'border',
      'bg-white',
      'px-3',
      'py-2',
      'text-sm',
      'placeholder-gray-400',
      'transition-colors',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'disabled:cursor-not-allowed',
      'disabled:opacity-50',
      'disabled:bg-gray-50'
    ];

    // State-dependent classes
    const stateClasses = error
      ? [
          'border-red-300',
          'text-red-900',
          'focus:border-red-500',
          'focus:ring-red-500'
        ]
      : [
          'border-gray-300',
          'text-gray-900',
          'focus:border-blue-500',
          'focus:ring-blue-500'
        ];

    // Icon padding classes
    const paddingClasses = [];
    if (LeftIcon) paddingClasses.push('pl-10');
    if (RightIcon || isPassword) paddingClasses.push('pr-10');

    // Combine classes
    const inputClasses = [
      ...baseClasses,
      ...stateClasses,
      ...paddingClasses,
      className
    ].join(' ');

    // Container classes
    const containerClasses = fullWidth ? 'w-full' : '';

    return (
      <div className={containerClasses}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}

        {/* Input container */}
        <div className="relative">
          {/* Left icon */}
          {LeftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <LeftIcon
                size={16}
                className={error ? 'text-red-400' : 'text-gray-400'}
              />
            </div>
          )}

          {/* Input field */}
          <input
            ref={ref}
            type={inputType}
            className={inputClasses}
            disabled={disabled || isLoading}
            {...props}
          />

          {/* Right icon or password toggle */}
          {(RightIcon || isPassword) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {isPassword ? (
                <button
                  type="button"
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              ) : RightIcon ? (
                <RightIcon
                  size={16}
                  className={error ? 'text-red-400' : 'text-gray-400'}
                />
              ) : null}
            </div>
          )}

          {/* Loading spinner */}
          {isLoading && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <div className="loading-spinner w-4 h-4" />
            </div>
          )}
        </div>

        {/* Helper text or error message */}
        {(error || helperText) && (
          <p
            className={`mt-1 text-sm ${
              error ? 'text-red-600' : 'text-gray-500'
            }`}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// Textarea component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  fullWidth?: boolean;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      error,
      helperText,
      fullWidth = true,
      className = '',
      disabled,
      ...props
    },
    ref
  ) => {
    // Base textarea classes
    const baseClasses = [
      'block',
      'w-full',
      'rounded-md',
      'border',
      'bg-white',
      'px-3',
      'py-2',
      'text-sm',
      'placeholder-gray-400',
      'transition-colors',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'disabled:cursor-not-allowed',
      'disabled:opacity-50',
      'disabled:bg-gray-50',
      'resize-vertical'
    ];

    // State-dependent classes
    const stateClasses = error
      ? [
          'border-red-300',
          'text-red-900',
          'focus:border-red-500',
          'focus:ring-red-500'
        ]
      : [
          'border-gray-300',
          'text-gray-900',
          'focus:border-blue-500',
          'focus:ring-blue-500'
        ];

    // Combine classes
    const textareaClasses = [...baseClasses, ...stateClasses, className].join(' ');

    // Container classes
    const containerClasses = fullWidth ? 'w-full' : '';

    return (
      <div className={containerClasses}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}

        {/* Textarea */}
        <textarea
          ref={ref}
          className={textareaClasses}
          disabled={disabled}
          {...props}
        />

        {/* Helper text or error message */}
        {(error || helperText) && (
          <p
            className={`mt-1 text-sm ${
              error ? 'text-red-600' : 'text-gray-500'
            }`}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

// Select component
interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  helperText?: string;
  fullWidth?: boolean;
  options: { value: string; label: string }[];
  placeholder?: string;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      label,
      error,
      helperText,
      fullWidth = true,
      className = '',
      disabled,
      options,
      placeholder,
      ...props
    },
    ref
  ) => {
    // Base select classes
    const baseClasses = [
      'block',
      'w-full',
      'rounded-md',
      'border',
      'bg-white',
      'px-3',
      'py-2',
      'text-sm',
      'transition-colors',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'disabled:cursor-not-allowed',
      'disabled:opacity-50',
      'disabled:bg-gray-50'
    ];

    // State-dependent classes
    const stateClasses = error
      ? [
          'border-red-300',
          'text-red-900',
          'focus:border-red-500',
          'focus:ring-red-500'
        ]
      : [
          'border-gray-300',
          'text-gray-900',
          'focus:border-blue-500',
          'focus:ring-blue-500'
        ];

    // Combine classes
    const selectClasses = [...baseClasses, ...stateClasses, className].join(' ');

    // Container classes
    const containerClasses = fullWidth ? 'w-full' : '';

    return (
      <div className={containerClasses}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}

        {/* Select */}
        <select
          ref={ref}
          className={selectClasses}
          disabled={disabled}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {/* Helper text or error message */}
        {(error || helperText) && (
          <p
            className={`mt-1 text-sm ${
              error ? 'text-red-600' : 'text-gray-500'
            }`}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';
