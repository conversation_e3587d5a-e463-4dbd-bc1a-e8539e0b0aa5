import React, { ButtonHTMLAttributes, forwardRef } from 'react';
import { LucideIcon } from 'lucide-react';

// Button variants
type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
type ButtonSize = 'sm' | 'md' | 'lg';

// Button props interface
interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  leftIcon?: LucideIcon;
  rightIcon?: LucideIcon;
  fullWidth?: boolean;
  children: React.ReactNode;
}

// Button component
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      isLoading = false,
      leftIcon: LeftIcon,
      rightIcon: RightIcon,
      fullWidth = false,
      disabled,
      className = '',
      children,
      ...props
    },
    ref
  ) => {
    // Base classes
    const baseClasses = [
      'inline-flex',
      'items-center',
      'justify-center',
      'rounded-md',
      'font-medium',
      'transition-all',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'disabled:opacity-50',
      'disabled:cursor-not-allowed',
      'disabled:pointer-events-none'
    ];

    // Variant classes
    const variantClasses = {
      primary: [
        'bg-blue-600',
        'text-white',
        'hover:bg-blue-700',
        'active:bg-blue-800',
        'focus:ring-blue-500',
        'shadow-sm'
      ],
      secondary: [
        'bg-gray-100',
        'text-gray-900',
        'hover:bg-gray-200',
        'active:bg-gray-300',
        'focus:ring-gray-500',
        'border',
        'border-gray-200'
      ],
      outline: [
        'bg-transparent',
        'text-gray-700',
        'border',
        'border-gray-300',
        'hover:bg-gray-50',
        'active:bg-gray-100',
        'focus:ring-gray-500'
      ],
      ghost: [
        'bg-transparent',
        'text-gray-700',
        'hover:bg-gray-100',
        'active:bg-gray-200',
        'focus:ring-gray-500'
      ],
      danger: [
        'bg-red-600',
        'text-white',
        'hover:bg-red-700',
        'active:bg-red-800',
        'focus:ring-red-500',
        'shadow-sm'
      ]
    };

    // Size classes
    const sizeClasses = {
      sm: ['px-3', 'py-1.5', 'text-sm', 'gap-1.5'],
      md: ['px-4', 'py-2', 'text-sm', 'gap-2'],
      lg: ['px-6', 'py-3', 'text-base', 'gap-2']
    };

    // Width classes
    const widthClasses = fullWidth ? ['w-full'] : [];

    // Combine all classes
    const buttonClasses = [
      ...baseClasses,
      ...variantClasses[variant],
      ...sizeClasses[size],
      ...widthClasses,
      className
    ].join(' ');

    // Icon size based on button size
    const iconSize = {
      sm: 14,
      md: 16,
      lg: 18
    }[size];

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading ? (
          <>
            <div className={`loading-spinner w-${iconSize/4} h-${iconSize/4}`} />
            <span>Loading...</span>
          </>
        ) : (
          <>
            {LeftIcon && <LeftIcon size={iconSize} />}
            {children}
            {RightIcon && <RightIcon size={iconSize} />}
          </>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Icon button component
interface IconButtonProps extends Omit<ButtonProps, 'children'> {
  icon: LucideIcon;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon: Icon, size = 'md', ...props }, ref) => {
    const iconSize = {
      sm: 14,
      md: 16,
      lg: 18
    }[size];

    return (
      <Button ref={ref} size={size} {...props}>
        <Icon size={iconSize} />
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// Button group component
interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({ children, className = '' }) => {
  return (
    <div className={`inline-flex rounded-md shadow-sm ${className}`} role="group">
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          const isFirst = index === 0;
          const isLast = index === React.Children.count(children) - 1;
          
          let additionalClasses = '';
          
          if (!isFirst && !isLast) {
            additionalClasses = 'rounded-none border-l-0';
          } else if (isFirst) {
            additionalClasses = 'rounded-r-none';
          } else if (isLast) {
            additionalClasses = 'rounded-l-none border-l-0';
          }
          
          return React.cloneElement(child, {
            className: `${child.props.className || ''} ${additionalClasses}`.trim()
          });
        }
        return child;
      })}
    </div>
  );
};
